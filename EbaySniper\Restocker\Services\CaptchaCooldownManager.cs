using System;
using System.Diagnostics;

namespace uBuyFirst.Restocker.Services
{
    /// <summary>
    /// Represents the type of cooldown currently active
    /// </summary>
    public enum CooldownType
    {
        None,
        Captcha,
        Logout
    }

    /// <summary>
    /// Manages captcha and logout detection and cooldown periods for restock operations.
    /// When a captcha or logout is detected, all restock purchasing is paused for 5 minutes.
    /// </summary>
    public static class CaptchaCooldownManager
    {
        private static DateTime? _cooldownStartTime;
        private static CooldownType _cooldownType = CooldownType.None;
        private static readonly TimeSpan CooldownDuration = TimeSpan.FromMinutes(5);
        private static readonly object _lock = new object();

        /// <summary>
        /// Gets whether restock operations are currently in cooldown due to captcha or logout detection
        /// </summary>
        public static bool IsInCooldown
        {
            get
            {
                lock (_lock)
                {
                    if (_cooldownStartTime == null)
                        return false;

                    var elapsed = DateTime.UtcNow - _cooldownStartTime.Value;
                    if (elapsed >= CooldownDuration)
                    {
                        // Cooldown period has expired, clear it
                        var cooldownTypeText = _cooldownType == CooldownType.Captcha ? "captcha" : "logout";
                        _cooldownStartTime = null;
                        _cooldownType = CooldownType.None;
                        Debug.WriteLine($"Restock {cooldownTypeText} cooldown period has expired. Resuming auto purchasing.");
                        return false;
                    }

                    return true;
                }
            }
        }

        /// <summary>
        /// Gets the remaining cooldown time, or null if not in cooldown
        /// </summary>
        public static TimeSpan? RemainingCooldownTime
        {
            get
            {
                lock (_lock)
                {
                    if (_cooldownStartTime == null)
                        return null;

                    var elapsed = DateTime.UtcNow - _cooldownStartTime.Value;
                    var remaining = CooldownDuration - elapsed;

                    return remaining > TimeSpan.Zero ? remaining : null;
                }
            }
        }

        /// <summary>
        /// Starts a 5-minute cooldown period for restock operations due to captcha detection
        /// </summary>
        public static void StartCooldown()
        {
            StartCooldown(CooldownType.Captcha);
        }

        /// <summary>
        /// Starts a 5-minute cooldown period for restock operations due to logout detection
        /// </summary>
        public static void StartLogoutCooldown()
        {
            StartCooldown(CooldownType.Logout);
        }

        /// <summary>
        /// Starts a 5-minute cooldown period for restock operations
        /// </summary>
        /// <param name="cooldownType">The type of cooldown to start</param>
        private static void StartCooldown(CooldownType cooldownType)
        {
            lock (_lock)
            {
                _cooldownStartTime = DateTime.UtcNow;
                _cooldownType = cooldownType;
                var cooldownTypeText = cooldownType == CooldownType.Captcha ? "captcha" : "logout";
                Debug.WriteLine($"Restock {cooldownTypeText} cooldown started at {_cooldownStartTime.Value:HH:mm:ss UTC}. Auto purchasing paused for 5 minutes.");
            }
        }

        /// <summary>
        /// Manually clears the cooldown period (for testing or manual override)
        /// </summary>
        public static void ClearCooldown()
        {
            lock (_lock)
            {
                var cooldownTypeText = _cooldownType == CooldownType.Captcha ? "captcha" :
                                      _cooldownType == CooldownType.Logout ? "logout" : "unknown";
                _cooldownStartTime = null;
                _cooldownType = CooldownType.None;
                Debug.WriteLine($"Restock {cooldownTypeText} cooldown manually cleared. Auto purchasing resumed.");
            }
        }

        /// <summary>
        /// Gets the current cooldown type
        /// </summary>
        /// <returns>The type of cooldown currently active, or None if not in cooldown</returns>
        public static CooldownType GetCooldownType()
        {
            lock (_lock)
            {
                return IsInCooldown ? _cooldownType : CooldownType.None;
            }
        }

        /// <summary>
        /// Checks if the given error message indicates a captcha scenario
        /// </summary>
        /// <param name="errorMessage">The error message to check</param>
        /// <returns>True if the error indicates a captcha scenario</returns>
        public static bool IsCaptchaError(string errorMessage)
        {
            if (string.IsNullOrEmpty(errorMessage))
                return false;

            // Check for the specific error message that indicates captcha
            return errorMessage.IndexOf("Session ID not found in session page HTML", StringComparison.OrdinalIgnoreCase) >= 0;
        }

        /// <summary>
        /// Checks if the given error message or response content indicates a logout scenario
        /// </summary>
        /// <param name="content">The error message or response content to check</param>
        /// <returns>True if the content indicates a logout scenario (signin redirect)</returns>
        public static bool IsLogoutError(string content)
        {
            if (string.IsNullOrEmpty(content))
                return false;

            // Check for our specific logout error message
            if (content.IndexOf("User logged out - signin redirect detected", StringComparison.OrdinalIgnoreCase) >= 0)
                return true;

            // Check for eBay signin redirect URL pattern (most common)
            if (content.IndexOf("signin.ebay.com/ws/eBayISAPI.dll?SignIn", StringComparison.OrdinalIgnoreCase) >= 0)
                return true;

            // Check for other logout indicators in HTML content
            if (content.IndexOf("signin.ebay.com", StringComparison.OrdinalIgnoreCase) >= 0 &&
                content.IndexOf("SignIn", StringComparison.OrdinalIgnoreCase) >= 0)
                return true;

            // Check for "Not logged in" or similar messages
            if (content.IndexOf("Not logged in", StringComparison.OrdinalIgnoreCase) >= 0)
                return true;

            return false;
        }

        /// <summary>
        /// Gets a user-friendly status message about the current cooldown state
        /// </summary>
        public static string GetStatusMessage()
        {
            if (!IsInCooldown)
                return "Restock auto purchasing is active";

            var remaining = RemainingCooldownTime;
            if (remaining.HasValue)
            {
                var minutes = (int)remaining.Value.TotalMinutes;
                var seconds = remaining.Value.Seconds;

                var reason = _cooldownType == CooldownType.Captcha ? "captcha" : "logout";
                return $"Restock auto purchasing paused due to {reason}. Resuming in {minutes}m {seconds}s";
            }

            return "Restock auto purchasing is active";
        }
    }
}
